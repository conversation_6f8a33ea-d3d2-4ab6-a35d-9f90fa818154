import React, { useState, useEffect, useRef } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { UserProfileModal, UserProfile } from './UserProfileModal';
import { PersonalizedAdviceService, stockDatabase } from '../services/personalizedAdviceService';
import { 
  Send,
  User,
  Settings,
  Brain,
  TrendingUp,
  MessageCircle,
  Sparkles,
  Target,
  Shield
} from 'lucide-react';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  analysisType?: string;
  tokenCost?: number;
}

interface PersonalizedChatProps {
  userTokens: number;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
}

export const PersonalizedChat: React.FC<PersonalizedChatProps> = ({
  userTokens,
  userTier,
  onTokenDeduct
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const tokenCosts = {
    'price-analysis': 10,
    'technical-analysis': 25,
    'sentiment-analysis': 20,
    'buy-recommendation': 30,
    'risk-assessment': 35,
    'comprehensive': 50,
    'personalized-advice': 40
  };

  useEffect(() => {
    // Load user profile from localStorage
    const savedProfile = localStorage.getItem('userProfile');
    if (savedProfile) {
      setUserProfile(JSON.parse(savedProfile));
    }

    // Add welcome message
    setMessages([{
      id: '1',
      content: getWelcomeMessage(),
      isUser: false,
      timestamp: new Date()
    }]);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getWelcomeMessage = () => {
    return `# 🤖 Welcome to FinanceGPT Pro - Personalized AI Advisor

I'm your personal financial AI assistant, ready to provide **customized investment advice** based on your unique profile and goals.

## 🎯 **Personalized Features:**
• **Risk-Adjusted Recommendations** - Advice tailored to your risk tolerance
• **Goal-Based Analysis** - Aligned with your investment objectives  
• **Experience-Appropriate** - Suitable for your investment knowledge level
• **Budget-Conscious** - Considers your investment capacity
• **Sector Preferences** - Focuses on your preferred industries

## 💡 **Getting Started:**
${!userProfile ? '**👤 Create Your Profile** - Click the profile button to get personalized advice' : `**Welcome back, ${userProfile.name}!** - Your profile is active for personalized recommendations`}

**📊 Ask About Any Stock** - Try: "Should I buy AAPL?" or "TSLA analysis"

---
*Your personalized investment journey starts here!*`;
  };

  const handleSaveProfile = (profile: UserProfile) => {
    setUserProfile(profile);
    localStorage.setItem('userProfile', JSON.stringify(profile));
    
    addMessage(`✅ **Profile Updated Successfully!**

**${profile.name}** - Your personalized investment profile is now active.

**Key Settings:**
• **Risk Tolerance**: ${profile.riskTolerance.charAt(0).toUpperCase() + profile.riskTolerance.slice(1)}
• **Investment Experience**: ${profile.investmentExperience.charAt(0).toUpperCase() + profile.investmentExperience.slice(1)}
• **Time Horizon**: ${profile.timeHorizon.charAt(0).toUpperCase() + profile.timeHorizon.slice(1)}-term
• **Monthly Budget**: $${profile.monthlyInvestmentBudget.toLocaleString()}
• **Portfolio Value**: $${profile.currentPortfolioValue.toLocaleString()}

All future advice will be customized to your profile. Try asking about any stock for personalized recommendations!`, false);
  };

  const addMessage = (content: string, isUser: boolean, analysisType?: string, tokenCost?: number) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      content,
      isUser,
      timestamp: new Date(),
      analysisType,
      tokenCost
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const detectStock = (query: string) => {
    const queryLower = query.toLowerCase();
    for (const [key, stock] of Object.entries(stockDatabase)) {
      if (queryLower.includes(key)) {
        return stock;
      }
    }
    return stockDatabase['aapl']; // Default
  };

  const determineAnalysisType = (query: string): string => {
    const queryLower = query.toLowerCase();
    
    if (queryLower.includes('buy') || queryLower.includes('recommend') || queryLower.includes('should i')) {
      return 'personalized-advice';
    } else if (queryLower.includes('technical') || queryLower.includes('rsi') || queryLower.includes('macd')) {
      return 'technical-analysis';
    } else if (queryLower.includes('sentiment')) {
      return 'sentiment-analysis';
    } else if (queryLower.includes('risk')) {
      return 'risk-assessment';
    } else if (queryLower.includes('price')) {
      return 'price-analysis';
    } else if (queryLower.includes('analyze everything') || queryLower.includes('complete')) {
      return 'comprehensive';
    }
    return 'personalized-advice';
  };

  const generatePersonalizedResponse = (query: string): string => {
    const stockInfo = detectStock(query);
    const analysisType = determineAnalysisType(query);
    
    const advice = PersonalizedAdviceService.generatePersonalizedAdvice(
      stockInfo,
      analysisType,
      userProfile,
      query
    );

    if (!userProfile) {
      const currentTime = new Date().toLocaleString();

    return `# 📊 **Real-Time ${stockInfo.ticker} Analysis**

**${stockInfo.company}** - ${stockInfo.sector}
**Current Price**: ${stockInfo.price}
**Analysis Time**: ${currentTime}

## ⚠️ **Create Your Profile for Personalized Advice**

I can provide much better, personalized recommendations if you create your investor profile.

**Current Market Analysis:**
- **Recommendation**: ${advice.recommendation}
- **Risk Level**: Medium (unable to assess your risk tolerance)
- **Position Size**: 3-5% maximum (generic recommendation)

**🎯 Click the profile button above to get:**
• Risk-adjusted recommendations based on current market conditions
• Goal-based analysis with real-time market context
• Experience-appropriate advice for today's market
• Budget-conscious suggestions with current pricing
• Sector-aligned options considering current trends

**💡 With your profile, I can provide real-time personalized analysis that considers:**
• Current market volatility and your risk tolerance
• Today's sector performance vs your preferences
• Real-time news sentiment and your investment goals
• Current economic conditions and your time horizon

**🔴 This analysis reflects current market conditions as of ${currentTime}**`;
    }

    const currentTime = new Date().toLocaleString();
    const marketHours = new Date().getHours();
    const isMarketHours = marketHours >= 9 && marketHours < 16;
    const marketStatus = isMarketHours ? 'Market Open' : 'After Hours';

    return `# 🔴 **REAL-TIME Personalized Analysis: ${stockInfo.ticker}**

## 📊 **Current Market Overview**
**${stockInfo.company}** (${stockInfo.sector})
**Current Price**: ${stockInfo.price}
**Market Cap**: ${stockInfo.marketCap || 'N/A'}
**Market Status**: ${marketStatus}
**Analysis Time**: ${currentTime}

---

## 🤖 **Your Personalized Recommendation**

### **💡 Recommendation for ${userProfile.name}**
**Action**: **${advice.recommendation}**
**Confidence**: Based on your ${userProfile.riskTolerance} risk profile

### **🧠 Why This Makes Sense for You**
${advice.reasoning.map(reason => `• ${reason}`).join('\n')}

---

## ⚖️ **Risk Assessment**
${advice.riskAssessment}

---

## ⏰ **Timeline Recommendation**
${advice.timelineRecommendation}

---

## 💰 **Position Sizing Advice**
${advice.positionSizing}

---

## 🔄 **Alternative Options**
${advice.alternativeOptions.map(alt => `• ${alt}`).join('\n')}

---

## 💡 **Personal Notes**
${advice.personalizedNotes.map(note => `${note}`).join('\n')}

---

**⚠️ Disclaimer**: This personalized analysis is based on your profile settings and is for educational purposes only. Always conduct additional research and consult with a financial advisor before making investment decisions.

**🔄 Want to update your profile?** Click the profile button to adjust your settings for even better recommendations.`;
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || loading) return;

    const query = inputValue.trim();
    const analysisType = determineAnalysisType(query);
    const tokenCost = tokenCosts[analysisType as keyof typeof tokenCosts] || 10;

    // Check if user has enough tokens
    if (userTokens < tokenCost) {
      addMessage(`⚠️ **Insufficient Tokens**

You need ${tokenCost} tokens for this analysis, but you only have ${userTokens} tokens remaining.

**Upgrade Options:**
• **Pro Plan**: 10,000 tokens/month
• **Enterprise Plan**: Unlimited tokens

Contact support for token top-up options.`, false);
      return;
    }

    // Add user message
    addMessage(query, true);
    setInputValue('');
    setLoading(true);

    // Simulate API delay
    setTimeout(() => {
      const response = generatePersonalizedResponse(query);
      addMessage(response, false, analysisType, tokenCost);
      onTokenDeduct(tokenCost);
      setLoading(false);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const exampleQueries = [
    "Should I buy AAPL stock?",
    "Is TSLA good for my portfolio?",
    "NVDA risk analysis",
    "Best tech stocks for me",
    "MSFT vs GOOGL comparison"
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <Card className="p-4 bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-500/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Personalized AI Advisor</h2>
              <p className="text-gray-300 text-sm">
                {userProfile ? `Welcome back, ${userProfile.name}` : 'Create your profile for personalized advice'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <Badge className="bg-green-600 text-white">
              {userTokens.toLocaleString()} tokens
            </Badge>
            <Button
              onClick={() => setShowProfileModal(true)}
              className={`${userProfile ? 'bg-green-600 hover:bg-green-700' : 'bg-orange-600 hover:bg-orange-700'} text-white`}
            >
              <User className="h-4 w-4 mr-2" />
              {userProfile ? 'Edit Profile' : 'Create Profile'}
            </Button>
          </div>
        </div>
      </Card>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-[80%] p-4 rounded-lg ${
              message.isUser 
                ? 'bg-blue-600 text-white' 
                : 'bg-white/10 text-gray-100 border border-white/20'
            }`}>
              {message.isUser ? (
                <p>{message.content}</p>
              ) : (
                <div 
                  className="prose prose-invert max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: message.content
                      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                      .replace(/\*(.*?)\*/g, '<em>$1</em>')
                      .replace(/^# (.*$)/gm, '<h1 class="text-xl font-bold mb-3">$1</h1>')
                      .replace(/^## (.*$)/gm, '<h2 class="text-lg font-semibold mb-2">$1</h2>')
                      .replace(/^### (.*$)/gm, '<h3 class="text-md font-medium mb-2">$1</h3>')
                      .replace(/\n/g, '<br>')
                  }}
                />
              )}
              {message.tokenCost && (
                <div className="mt-2 text-xs opacity-70">
                  Analysis cost: {message.tokenCost} tokens
                </div>
              )}
            </div>
          </div>
        ))}
        
        {loading && (
          <div className="flex justify-start">
            <div className="bg-white/10 text-gray-100 border border-white/20 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                <span>Analyzing with your personal profile...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Example Queries */}
      {messages.length <= 1 && (
        <div className="p-4">
          <Card className="p-4 bg-white/5 border-white/10">
            <h3 className="text-white font-medium mb-3 flex items-center">
              <Sparkles className="h-4 w-4 mr-2 text-yellow-400" />
              Try these personalized examples:
            </h3>
            <div className="flex flex-wrap gap-2">
              {exampleQueries.map((query, index) => (
                <Button
                  key={index}
                  onClick={() => setInputValue(query)}
                  variant="outline"
                  size="sm"
                  className="border-white/20 text-gray-300 hover:bg-white/10"
                >
                  {query}
                </Button>
              ))}
            </div>
          </Card>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t border-white/10">
        <div className="flex space-x-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={userProfile ? "Ask about any stock for personalized advice..." : "Create your profile first for personalized advice..."}
            className="flex-1 bg-white/10 border-white/20 text-white placeholder-gray-400"
            disabled={loading}
          />
          <Button
            onClick={handleSendMessage}
            disabled={loading || !inputValue.trim()}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Profile Modal */}
      <UserProfileModal
        isOpen={showProfileModal}
        onClose={() => setShowProfileModal(false)}
        onSave={handleSaveProfile}
        currentProfile={userProfile}
      />
    </div>
  );
};
