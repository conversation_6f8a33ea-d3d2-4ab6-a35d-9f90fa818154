import React, { useState, useEffect, useRef } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { 
  TrendingUp, 
  BarChart3, 
  Activity, 
  AlertTriangle, 
  Target,
  Zap,
  PieChart,
  LineChart
} from 'lucide-react';

interface AnalyticsResult {
  ticker: string;
  analysis_type: string;
  result: string;
  timestamp: string;
}

interface AdvancedAnalyticsProps {
  userTokens: number;
  onTokenDeduct: (amount: number) => void;
}

export const AdvancedAnalytics: React.FC<AdvancedAnalyticsProps> = ({
  userTokens,
  onTokenDeduct
}) => {
  const [selectedTicker, setSelectedTicker] = useState('');
  const [selectedAnalysis, setSelectedAnalysis] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<AnalyticsResult[]>([]);
  const [currentResult, setCurrentResult] = useState<string>('');
  const resultsRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to results when they appear
  useEffect(() => {
    if (currentResult && resultsRef.current) {
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 100);
    }
  }, [currentResult]);

  const analysisTypes = [
    {
      id: 'momentum',
      name: 'Momentum Analysis',
      description: 'Analyze price momentum and trend strength',
      icon: TrendingUp,
      tokenCost: 15,
      color: 'bg-green-100 text-green-800',
      features: ['Short-term momentum', 'Medium-term trends', 'Long-term direction', 'Trend strength scoring']
    },
    {
      id: 'volatility',
      name: 'Volatility Analysis',
      description: 'Assess price volatility and risk levels',
      icon: Activity,
      tokenCost: 12,
      color: 'bg-orange-100 text-orange-800',
      features: ['Current volatility', 'Historical comparison', 'Risk assessment', 'Volatility percentiles']
    },
    {
      id: 'risk',
      name: 'Risk Metrics',
      description: 'Comprehensive risk assessment and metrics',
      icon: AlertTriangle,
      tokenCost: 20,
      color: 'bg-red-100 text-red-800',
      features: ['Value at Risk (VaR)', 'Sharpe ratio', 'Sortino ratio', 'Maximum drawdown']
    },
    {
      id: 'correlation',
      name: 'Correlation Analysis',
      description: 'Compare relationships between stocks',
      icon: BarChart3,
      tokenCost: 18,
      color: 'bg-blue-100 text-blue-800',
      features: ['Stock correlations', 'Market relationships', 'Sector analysis', 'Diversification insights']
    },
    {
      id: 'technical_patterns',
      name: 'Pattern Recognition',
      description: 'Identify technical chart patterns',
      icon: LineChart,
      tokenCost: 25,
      color: 'bg-purple-100 text-purple-800',
      features: ['Chart patterns', 'Support/resistance', 'Breakout signals', 'Pattern reliability']
    },
    {
      id: 'sector_analysis',
      name: 'Sector Performance',
      description: 'Analyze sector-wide performance trends',
      icon: PieChart,
      tokenCost: 22,
      color: 'bg-indigo-100 text-indigo-800',
      features: ['Sector comparison', 'Relative performance', 'Industry trends', 'Best/worst performers']
    }
  ];

  const popularTickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM'];

  const handleAnalysis = async (analysisType: string) => {
    if (!selectedTicker.trim()) {
      alert('Please enter a stock ticker symbol');
      return;
    }

    const analysis = analysisTypes.find(a => a.id === analysisType);
    if (!analysis) return;

    if (userTokens < analysis.tokenCost) {
      alert(`Insufficient tokens! ${analysis.name} costs ${analysis.tokenCost} tokens.`);
      return;
    }

    setIsAnalyzing(true);
    setSelectedAnalysis(analysisType);
    onTokenDeduct(analysis.tokenCost);

    try {
      const response = await fetch(`http://localhost:8125/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `get advanced analytics for ${selectedTicker} with analysis type ${analysisType}`
        }),
      });

      const data = await response.json();
      
      let analysisResult = '';
      if (data.messages && data.messages.length > 0) {
        const lastMessage = data.messages[data.messages.length - 1];
        analysisResult = lastMessage.content;
      } else {
        // Mock results for demonstration
        analysisResult = generateMockAnalysis(selectedTicker, analysisType);
      }

      const newResult: AnalyticsResult = {
        ticker: selectedTicker.toUpperCase(),
        analysis_type: analysisType,
        result: analysisResult,
        timestamp: new Date().toISOString()
      };

      setResults(prev => [newResult, ...prev.slice(0, 9)]); // Keep last 10 results
      setCurrentResult(analysisResult);

    } catch (error) {
      console.error('Analysis error:', error);
      let errorMessage = `Error performing ${analysis.name}: `;

      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage += 'Backend service is not available. Please ensure the backend server is running on port 8125.';
      } else {
        errorMessage += error instanceof Error ? error.message : String(error);
      }

      setCurrentResult(errorMessage);
      // Refund tokens on error
      onTokenDeduct(-analysis.tokenCost);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generateMockAnalysis = (ticker: string, analysisType: string): string => {
    const mockResults = {
      momentum: `📈 **Momentum Analysis for ${ticker}**

**Trend Strength**: Strong Uptrend
**Overall Momentum Score**: 0.1247

**Timeframe Analysis**:
- Short-term (10 days): +3.45%
- Medium-term (30 days): +8.12%
- Long-term (60 days): +15.67%

**Interpretation**:
Strong Uptrend indicates positive price momentum across all timeframes. The stock shows consistent upward movement with accelerating momentum in recent periods.`,

      volatility: `📊 **Volatility Analysis for ${ticker}**

**Risk Level**: Medium
**Current Volatility**: 24.56% (annualized)
**Historical Volatility**: 28.34% (annualized)
**Volatility Percentile**: 65.2%

**Interpretation**:
Current volatility is at the 65th percentile of historical levels. Risk level is classified as Medium, indicating moderate price fluctuations compared to historical norms.`,

      risk: `⚠️ **Risk Metrics for ${ticker}**

**Volatility**: 24.56% (annualized)
**Value at Risk (95%)**: -2.34%
**Maximum Drawdown**: -18.45%
**Sharpe Ratio**: 1.247
**Sortino Ratio**: 1.689

**Interpretation**:
These metrics indicate a favorable risk-return profile. The Sharpe ratio above 1.0 suggests good risk-adjusted returns, while the Sortino ratio shows even better downside risk management.`
    };

    return mockResults[analysisType as keyof typeof mockResults] || `Analysis completed for ${ticker} (${analysisType})`;
  };

  const getAnalysisIcon = (analysisType: string) => {
    const analysis = analysisTypes.find(a => a.id === analysisType);
    if (!analysis) return BarChart3;
    return analysis.icon;
  };

  const getAnalysisColor = (analysisType: string) => {
    const analysis = analysisTypes.find(a => a.id === analysisType);
    return analysis?.color || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Zap className="h-8 w-8 text-purple-600" />
        <div>
          <h2 className="text-2xl font-bold text-white">Advanced Analytics</h2>
          <p className="text-gray-300">Professional-grade financial analysis tools</p>
        </div>
      </div>

      {/* Ticker Input */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Select Stock for Analysis</h3>
        <div className="space-y-4">
          <div className="flex space-x-4">
            <Input
              type="text"
              placeholder="Enter ticker symbol (e.g., AAPL)"
              value={selectedTicker}
              onChange={(e) => setSelectedTicker(e.target.value.toUpperCase())}
              className="flex-1"
              maxLength={10}
            />
            <Button
              variant="outline"
              onClick={() => setSelectedTicker('')}
              disabled={!selectedTicker}
            >
              Clear
            </Button>
          </div>
          
          {/* Popular Tickers */}
          <div>
            <p className="text-sm text-gray-300 mb-2">Popular stocks:</p>
            <div className="flex flex-wrap gap-2">
              {popularTickers.map((ticker) => (
                <Button
                  key={ticker}
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedTicker(ticker)}
                  className={selectedTicker === ticker ? 'bg-blue-50 border-blue-300' : ''}
                >
                  {ticker}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* Analysis Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Analysis Tools</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {analysisTypes.map((analysis) => {
            const IconComponent = analysis.icon;
            return (
              <div
                key={analysis.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <IconComponent className="h-5 w-5 text-gray-300" />
                    <h4 className="font-semibold text-white">{analysis.name}</h4>
                  </div>
                  <Badge className={analysis.color} variant="secondary">
                    {analysis.tokenCost} tokens
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-300 mb-3">{analysis.description}</p>
                
                <div className="space-y-2 mb-4">
                  {analysis.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2 text-xs text-gray-500">
                      <Target className="h-3 w-3" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
                
                <Button
                  onClick={() => handleAnalysis(analysis.id)}
                  disabled={!selectedTicker || isAnalyzing || userTokens < analysis.tokenCost}
                  className="w-full"
                  variant={selectedAnalysis === analysis.id && isAnalyzing ? "secondary" : "default"}
                >
                  {selectedAnalysis === analysis.id && isAnalyzing ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Analyzing...</span>
                    </div>
                  ) : (
                    `Run Analysis`
                  )}
                </Button>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Current Result */}
      {currentResult && (
        <Card className="p-6" ref={resultsRef}>
          <h3 className="text-lg font-semibold text-white mb-4">Analysis Results</h3>
          <div className="bg-white/10 backdrop-blur-sm p-4 rounded-lg border border-white/20">
            <pre className="whitespace-pre-wrap text-sm text-gray-100">
              {currentResult}
            </pre>
          </div>
          <div className="mt-4 text-xs text-gray-500">
            ⚠️ Disclaimer: Advanced analytics are for informational purposes only and should not be considered as investment advice.
          </div>
        </Card>
      )}

      {/* Analysis History */}
      {results.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Analysis History</h3>
          <div className="space-y-3">
            {results.slice(0, 5).map((result, index) => {
              const IconComponent = getAnalysisIcon(result.analysis_type);
              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => setCurrentResult(result.result)}
                >
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-4 w-4 text-gray-300" />
                    <div>
                      <span className="font-medium text-white">{result.ticker}</span>
                      <span className="text-gray-400 ml-2">
                        {analysisTypes.find(a => a.id === result.analysis_type)?.name}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getAnalysisColor(result.analysis_type)} variant="secondary">
                      {result.analysis_type}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
      )}
    </div>
  );
};
