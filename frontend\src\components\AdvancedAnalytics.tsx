import React, { useState, useEffect, useRef } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { 
  TrendingUp, 
  BarChart3, 
  Activity, 
  AlertTriangle, 
  Target,
  Zap,
  PieChart,
  LineChart
} from 'lucide-react';

interface AnalyticsResult {
  ticker: string;
  analysis_type: string;
  result: string;
  timestamp: string;
}

interface AdvancedAnalyticsProps {
  userTokens: number;
  onTokenDeduct: (amount: number) => void;
}

export const AdvancedAnalytics: React.FC<AdvancedAnalyticsProps> = ({
  userTokens,
  onTokenDeduct
}) => {
  const [selectedTicker, setSelectedTicker] = useState('');
  const [selectedAnalysis, setSelectedAnalysis] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<AnalyticsResult[]>([]);
  const [currentResult, setCurrentResult] = useState<string>('');
  const resultsRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to results when they appear
  useEffect(() => {
    if (currentResult && resultsRef.current) {
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 100);
    }
  }, [currentResult]);

  const analysisTypes = [
    {
      id: 'momentum',
      name: 'Momentum Analysis',
      description: 'Analyze price momentum and trend strength',
      icon: TrendingUp,
      tokenCost: 15,
      color: 'bg-green-100 text-green-800',
      features: ['Short-term momentum', 'Medium-term trends', 'Long-term direction', 'Trend strength scoring']
    },
    {
      id: 'volatility',
      name: 'Volatility Analysis',
      description: 'Assess price volatility and risk levels',
      icon: Activity,
      tokenCost: 12,
      color: 'bg-orange-100 text-orange-800',
      features: ['Current volatility', 'Historical comparison', 'Risk assessment', 'Volatility percentiles']
    },
    {
      id: 'risk',
      name: 'Risk Metrics',
      description: 'Comprehensive risk assessment and metrics',
      icon: AlertTriangle,
      tokenCost: 20,
      color: 'bg-red-100 text-red-800',
      features: ['Value at Risk (VaR)', 'Sharpe ratio', 'Sortino ratio', 'Maximum drawdown']
    },
    {
      id: 'correlation',
      name: 'Correlation Analysis',
      description: 'Compare relationships between stocks',
      icon: BarChart3,
      tokenCost: 18,
      color: 'bg-blue-100 text-blue-800',
      features: ['Stock correlations', 'Market relationships', 'Sector analysis', 'Diversification insights']
    },
    {
      id: 'technical_patterns',
      name: 'Pattern Recognition',
      description: 'Identify technical chart patterns',
      icon: LineChart,
      tokenCost: 25,
      color: 'bg-purple-100 text-purple-800',
      features: ['Chart patterns', 'Support/resistance', 'Breakout signals', 'Pattern reliability']
    },
    {
      id: 'sector_analysis',
      name: 'Sector Performance',
      description: 'Analyze sector-wide performance trends',
      icon: PieChart,
      tokenCost: 22,
      color: 'bg-indigo-100 text-indigo-800',
      features: ['Sector comparison', 'Relative performance', 'Industry trends', 'Best/worst performers']
    }
  ];

  const popularTickers = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'JPM'];

  const handleAnalysis = async (analysisType: string) => {
    if (!selectedTicker.trim()) {
      alert('Please enter a stock ticker symbol');
      return;
    }

    const analysis = analysisTypes.find(a => a.id === analysisType);
    if (!analysis) return;

    if (userTokens < analysis.tokenCost) {
      alert(`Insufficient tokens! ${analysis.name} costs ${analysis.tokenCost} tokens.`);
      return;
    }

    setIsAnalyzing(true);
    setSelectedAnalysis(analysisType);
    onTokenDeduct(analysis.tokenCost);

    try {
      // Simulate API delay for realistic experience
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

      // Generate mock analysis results
      const analysisResult = generateMockAnalysis(selectedTicker, analysisType);

      const newResult: AnalyticsResult = {
        ticker: selectedTicker.toUpperCase(),
        analysis_type: analysisType,
        result: analysisResult,
        timestamp: new Date().toISOString()
      };

      setResults(prev => [newResult, ...prev.slice(0, 9)]); // Keep last 10 results
      setCurrentResult(analysisResult);

    } catch (error) {
      console.error('Analysis error:', error);
      const errorMessage = `Error performing ${analysis.name}: ${error instanceof Error ? error.message : String(error)}`;
      setCurrentResult(errorMessage);
      // Refund tokens on error
      onTokenDeduct(-analysis.tokenCost);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generateMockAnalysis = (ticker: string, analysisType: string): string => {
    const currentTime = new Date().toLocaleString();
    const tickerUpper = ticker.toUpperCase();

    // Generate realistic random data
    const generateRandomPercent = () => ((Math.random() - 0.5) * 20).toFixed(1);
    const generateRandomRatio = () => (Math.random() * 3 + 0.5).toFixed(2);

    const mockResults = {
      momentum: `📈 **Advanced Momentum Analysis for ${tickerUpper}**
*Analysis completed at ${currentTime}*

**📊 MOMENTUM INDICATORS**
• **Short-term Momentum (5-day)**: ${generateRandomPercent()}%
• **Medium-term Momentum (20-day)**: ${generateRandomPercent()}%
• **Long-term Momentum (50-day)**: ${generateRandomPercent()}%

**🎯 TECHNICAL INDICATORS**
• **RSI (14-day)**: ${(Math.random() * 40 + 30).toFixed(1)} ${Math.random() > 0.5 ? '(Neutral zone)' : '(Approaching overbought)'}
• **MACD Signal**: ${Math.random() > 0.5 ? 'Bullish crossover detected' : 'Bearish divergence observed'}
• **Price vs 20-day MA**: ${generateRandomPercent()}% ${Math.random() > 0.5 ? 'above' : 'below'}

**⚡ MOMENTUM SCORE**: ${(Math.random() * 4 + 6).toFixed(1)}/10

**🔍 INTERPRETATION**:
${Math.random() > 0.5 ?
  'Strong momentum signals suggest continued upward movement. Watch for potential resistance levels.' :
  'Mixed momentum signals indicate consolidation phase. Consider waiting for clearer directional signals.'}`,

      volatility: `📊 **Advanced Volatility Analysis for ${tickerUpper}**
*Analysis completed at ${currentTime}*

**📈 VOLATILITY METRICS**
• **Current Volatility (30-day)**: ${(Math.random() * 20 + 15).toFixed(1)}%
• **Historical Average (1-year)**: ${(Math.random() * 15 + 18).toFixed(1)}%
• **Volatility Percentile**: ${Math.floor(Math.random() * 40 + 40)}th percentile

**⚖️ RISK METRICS**
• **Value at Risk (95%)**: -${(Math.random() * 2 + 2).toFixed(1)}%
• **Maximum Drawdown (6-month)**: -${(Math.random() * 10 + 8).toFixed(1)}%
• **Volatility Trend**: ${Math.random() > 0.5 ? 'Increasing' : 'Decreasing'}

**🔍 INTERPRETATION**:
${Math.random() > 0.5 ?
  'Elevated volatility suggests increased market uncertainty. Consider reducing position sizes.' :
  'Normal volatility levels indicate stable trading conditions.'}`,

      risk: `⚖️ **Comprehensive Risk Metrics for ${tickerUpper}**
*Analysis completed at ${currentTime}*

**📊 SYSTEMATIC RISK**
• **Beta (vs S&P 500)**: ${generateRandomRatio()} ${Math.random() > 0.5 ? '(More volatile than market)' : '(Less volatile than market)'}
• **Correlation with Market**: ${(Math.random() * 0.4 + 0.6).toFixed(2)}

**📈 RISK-ADJUSTED RETURNS**
• **Sharpe Ratio**: ${generateRandomRatio()} ${Math.random() > 0.5 ? '(Excellent)' : '(Good)'}
• **Sortino Ratio**: ${(Math.random() * 2 + 1.5).toFixed(2)} (Downside risk-adjusted)

**⚠️ DOWNSIDE RISK**
• **Maximum Drawdown**: -${(Math.random() * 15 + 10).toFixed(1)}%
• **Value at Risk (95%)**: -${(Math.random() * 3 + 3).toFixed(1)}%

**🔍 INTERPRETATION**:
${Math.random() > 0.5 ?
  'Risk metrics indicate favorable risk-return profile with good downside protection.' :
  'Mixed risk signals suggest careful position sizing and monitoring.'}`,

      correlation: `🔗 **Correlation Analysis for ${tickerUpper}**
*Analysis completed at ${currentTime}*

**📊 MARKET CORRELATIONS**
• **S&P 500**: ${(Math.random() * 0.6 + 0.4).toFixed(2)}
• **NASDAQ**: ${(Math.random() * 0.7 + 0.3).toFixed(2)}
• **Sector ETF**: ${(Math.random() * 0.8 + 0.2).toFixed(2)}

**🔍 DIVERSIFICATION BENEFIT**: ${Math.random() > 0.5 ? 'High' : 'Moderate'}

**🎯 INTERPRETATION**:
Correlation analysis helps assess diversification benefits and systemic risk exposure.`,

      technical_patterns: `📊 **Pattern Recognition Analysis for ${tickerUpper}**
*Analysis completed at ${currentTime}*

**🎯 DETECTED PATTERNS**
• **Primary Pattern**: ${Math.random() > 0.5 ? 'Ascending Triangle' : Math.random() > 0.5 ? 'Bull Flag' : 'Double Bottom'}
• **Reliability Score**: ${(Math.random() * 30 + 70).toFixed(0)}%
• **Formation Period**: ${Math.floor(Math.random() * 20 + 10)} days

**📈 SUPPORT & RESISTANCE**
• **Key Support**: $${(Math.random() * 200 + 50).toFixed(2)}
• **Key Resistance**: $${(Math.random() * 200 + 100).toFixed(2)}
• **Breakout Target**: $${(Math.random() * 250 + 150).toFixed(2)}

**📊 VOLUME ANALYSIS**
• **Volume Confirmation**: ${Math.random() > 0.5 ? 'Strong' : 'Weak'}
• **Average Volume**: ${(Math.random() * 5 + 1).toFixed(1)}M shares
• **Breakout Volume**: ${Math.random() > 0.5 ? 'Above average' : 'Below average'}

**🎯 TRADING IMPLICATIONS**:
${Math.random() > 0.5 ?
  'Pattern suggests potential upside breakout. Watch for volume confirmation on any break above resistance.' :
  'Pattern indicates consolidation phase. Wait for clear directional break with volume confirmation.'}

**⚠️ RISK MANAGEMENT**:
• **Stop Loss**: $${(Math.random() * 150 + 40).toFixed(2)}
• **Risk/Reward Ratio**: ${(Math.random() * 2 + 1).toFixed(1)}:1`,

      sector_analysis: `🏢 **Sector Performance Analysis for ${tickerUpper}**
*Analysis completed at ${currentTime}*

**📊 SECTOR METRICS**
• **Sector**: ${Math.random() > 0.5 ? 'Technology' : Math.random() > 0.5 ? 'Healthcare' : 'Financial Services'}
• **Sector Performance (YTD)**: ${generateRandomPercent()}%
• **Relative to S&P 500**: ${generateRandomPercent()}%
• **Sector Rank**: ${Math.floor(Math.random() * 5 + 1)}/11 sectors

**🎯 PEER COMPARISON**
• **vs Sector Average**: ${generateRandomPercent()}%
• **Sector Leadership**: ${Math.random() > 0.5 ? 'Leading performer' : 'Lagging performer'}
• **Market Cap Rank**: Top ${Math.floor(Math.random() * 20 + 5)}%

**📈 SECTOR TRENDS**
• **Momentum**: ${Math.random() > 0.5 ? 'Positive' : 'Negative'}
• **Institutional Flow**: ${Math.random() > 0.5 ? 'Net inflows' : 'Net outflows'}
• **Analyst Sentiment**: ${Math.random() > 0.5 ? 'Bullish' : 'Neutral'}
• **Earnings Revisions**: ${Math.random() > 0.5 ? 'Upward' : 'Downward'}

**🔍 SECTOR OUTLOOK**:
${Math.random() > 0.5 ?
  'Sector showing strong fundamentals with positive earnings revisions and institutional support.' :
  'Sector facing headwinds but individual stock selection and timing remain key for outperformance.'}

**⚠️ SECTOR RISKS**:
• Regulatory environment changes
• Economic cycle sensitivity
• Competitive dynamics
• Technology disruption`
    };

    return mockResults[analysisType as keyof typeof mockResults] ||
           `📊 **${analysisType.toUpperCase()} Analysis for ${tickerUpper}**\n*Analysis completed at ${currentTime}*\n\nDetailed ${analysisType} analysis has been completed.`;
  };

  const getAnalysisIcon = (analysisType: string) => {
    const analysis = analysisTypes.find(a => a.id === analysisType);
    if (!analysis) return BarChart3;
    return analysis.icon;
  };

  const getAnalysisColor = (analysisType: string) => {
    const analysis = analysisTypes.find(a => a.id === analysisType);
    return analysis?.color || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Zap className="h-8 w-8 text-purple-600" />
        <div>
          <h2 className="text-2xl font-bold text-white">Advanced Analytics</h2>
          <p className="text-gray-300">Professional-grade financial analysis tools</p>
        </div>
      </div>

      {/* Ticker Input */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Select Stock for Analysis</h3>
        <div className="space-y-4">
          <div className="flex space-x-4">
            <Input
              type="text"
              placeholder="Enter ticker symbol (e.g., AAPL)"
              value={selectedTicker}
              onChange={(e) => setSelectedTicker(e.target.value.toUpperCase())}
              className="flex-1"
              maxLength={10}
            />
            <Button
              variant="outline"
              onClick={() => setSelectedTicker('')}
              disabled={!selectedTicker}
            >
              Clear
            </Button>
          </div>
          
          {/* Popular Tickers */}
          <div>
            <p className="text-sm text-gray-300 mb-2">Popular stocks:</p>
            <div className="flex flex-wrap gap-2">
              {popularTickers.map((ticker) => (
                <Button
                  key={ticker}
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedTicker(ticker)}
                  className={selectedTicker === ticker ? 'bg-blue-50 border-blue-300' : ''}
                >
                  {ticker}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* Analysis Options */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Analysis Tools</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {analysisTypes.map((analysis) => {
            const IconComponent = analysis.icon;
            return (
              <div
                key={analysis.id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <IconComponent className="h-5 w-5 text-gray-300" />
                    <h4 className="font-semibold text-white">{analysis.name}</h4>
                  </div>
                  <Badge className={analysis.color} variant="secondary">
                    {analysis.tokenCost} tokens
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-300 mb-3">{analysis.description}</p>
                
                <div className="space-y-2 mb-4">
                  {analysis.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2 text-xs text-gray-500">
                      <Target className="h-3 w-3" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
                
                <Button
                  onClick={() => handleAnalysis(analysis.id)}
                  disabled={!selectedTicker || isAnalyzing || userTokens < analysis.tokenCost}
                  className="w-full"
                  variant={selectedAnalysis === analysis.id && isAnalyzing ? "secondary" : "default"}
                >
                  {selectedAnalysis === analysis.id && isAnalyzing ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Analyzing...</span>
                    </div>
                  ) : (
                    `Run Analysis`
                  )}
                </Button>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Current Result */}
      {currentResult && (
        <Card className="p-6" ref={resultsRef}>
          <h3 className="text-lg font-semibold text-white mb-4">Analysis Results</h3>
          <div className="bg-white/10 backdrop-blur-sm p-4 rounded-lg border border-white/20">
            <pre className="whitespace-pre-wrap text-sm text-gray-100">
              {currentResult}
            </pre>
          </div>
          <div className="mt-4 text-xs text-gray-500">
            ⚠️ Disclaimer: Advanced analytics are for informational purposes only and should not be considered as investment advice.
          </div>
        </Card>
      )}

      {/* Analysis History */}
      {results.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Recent Analysis History</h3>
          <div className="space-y-3">
            {results.slice(0, 5).map((result, index) => {
              const IconComponent = getAnalysisIcon(result.analysis_type);
              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => setCurrentResult(result.result)}
                >
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-4 w-4 text-gray-300" />
                    <div>
                      <span className="font-medium text-white">{result.ticker}</span>
                      <span className="text-gray-400 ml-2">
                        {analysisTypes.find(a => a.id === result.analysis_type)?.name}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getAnalysisColor(result.analysis_type)} variant="secondary">
                      {result.analysis_type}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
      )}
    </div>
  );
};
