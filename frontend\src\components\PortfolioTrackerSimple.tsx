import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { HoldingsManager } from './HoldingsManager';
import { PerformanceDashboard } from './PerformanceDashboard';
import { portfolioApi, Portfolio, PortfolioHolding } from '../services/portfolioApi';
import {
  PieChart,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  BarChart3,
  Plus
} from 'lucide-react';

interface PortfolioTrackerProps {
  userTokens: number;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
}

export const PortfolioTracker: React.FC<PortfolioTrackerProps> = ({
  userTokens,
  userTier,
  onTokenDeduct
}) => {
  const [portfolios, setPortfolios] = useState<Portfolio[]>([]);
  const [selectedPortfolio, setSelectedPortfolio] = useState<Portfolio | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeView, setActiveView] = useState<'overview' | 'holdings' | 'performance'>('overview');

  // Load portfolio data
  useEffect(() => {
    const loadPortfolios = async () => {
      try {
        setLoading(true);
        // Use demo data for now
        const demoPortfolio = portfolioApi.getDemoPortfolio();
        setPortfolios([demoPortfolio]);
        setSelectedPortfolio(demoPortfolio);
      } catch (error) {
        console.error('Error loading portfolios:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPortfolios();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  const handleAddHolding = async (ticker: string, shares: number, price: number) => {
    // Mock implementation for demo
    console.log('Adding holding:', { ticker, shares, price });
  };

  const handleUpdateHolding = async (ticker: string, shares: number, price: number) => {
    // Mock implementation for demo
    console.log('Updating holding:', { ticker, shares, price });
  };

  const handleRemoveHolding = async (ticker: string) => {
    // Mock implementation for demo
    console.log('Removing holding:', ticker);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300">Loading your portfolio...</p>
        </div>
      </div>
    );
  }

  if (!selectedPortfolio) {
    return (
      <div className="text-center py-12">
        <PieChart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Portfolio Found</h3>
        <p className="text-gray-300 mb-6">Create your first portfolio to start tracking your investments.</p>
        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
          <Plus className="h-4 w-4 mr-2" />
          Create Portfolio
        </Button>
      </div>
    );
  }

  const viewTabs = [
    { id: 'overview', name: 'Overview', icon: DollarSign },
    { id: 'holdings', name: 'Holdings', icon: Target },
    { id: 'performance', name: 'Performance', icon: BarChart3 }
  ];

  return (
    <div className="space-y-6">
      {/* Portfolio Header with Tabs */}
      <Card className="p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">{selectedPortfolio.name}</h2>
            <p className="text-gray-300">
              Last updated: {new Date(selectedPortfolio.last_updated).toLocaleDateString()}
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            <Badge className={`${
              selectedPortfolio.total_gain_loss >= 0 ? 'bg-green-600' : 'bg-red-600'
            } text-white px-3 py-1`}>
              {formatPercent(selectedPortfolio.total_gain_loss_percent)}
            </Badge>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
          {viewTabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ${
                  activeView === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                <IconComponent className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </div>
      </Card>

      {/* Portfolio Overview */}
      {activeView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-6 bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/30">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Portfolio Value</p>
                <p className="text-2xl font-bold text-white">
                  {formatCurrency(selectedPortfolio.total_value)}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-400" />
            </div>
          </Card>

          <Card className={`p-6 ${
            selectedPortfolio.total_gain_loss >= 0 
              ? 'bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/30'
              : 'bg-gradient-to-br from-red-900/20 to-rose-900/20 border-red-500/30'
          }`}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Total Gain/Loss</p>
                <p className={`text-2xl font-bold ${
                  selectedPortfolio.total_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {formatCurrency(selectedPortfolio.total_gain_loss)}
                </p>
                <p className={`text-sm ${
                  selectedPortfolio.total_gain_loss >= 0 ? 'text-green-300' : 'text-red-300'
                }`}>
                  {formatPercent(selectedPortfolio.total_gain_loss_percent)}
                </p>
              </div>
              {selectedPortfolio.total_gain_loss >= 0 ? (
                <TrendingUp className="h-8 w-8 text-green-400" />
              ) : (
                <TrendingDown className="h-8 w-8 text-red-400" />
              )}
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-br from-purple-900/20 to-pink-900/20 border-purple-500/30">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-300 text-sm">Holdings Count</p>
                <p className="text-2xl font-bold text-white">
                  {selectedPortfolio.holdings.length}
                </p>
                <p className="text-sm text-gray-300">
                  Cash: {formatCurrency(selectedPortfolio.cash_balance)}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-400" />
            </div>
          </Card>
        </div>
      )}

      {/* Holdings View */}
      {activeView === 'holdings' && (
        <HoldingsManager
          holdings={selectedPortfolio.holdings}
          onAddHolding={handleAddHolding}
          onUpdateHolding={handleUpdateHolding}
          onRemoveHolding={handleRemoveHolding}
          userTier={userTier}
        />
      )}

      {/* Performance View */}
      {activeView === 'performance' && (
        <PerformanceDashboard
          portfolio={selectedPortfolio}
          userTier={userTier}
          onTokenDeduct={onTokenDeduct}
        />
      )}

      {/* Holdings List for Overview */}
      {activeView === 'overview' && (
        <Card className="p-6">
          <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
            <Target className="h-5 w-5 mr-2 text-blue-400" />
            Holdings Overview
          </h3>
          <div className="space-y-3">
            {selectedPortfolio.holdings.map((holding) => (
              <div key={holding.ticker} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{holding.ticker}</span>
                  </div>
                  <div>
                    <p className="text-white font-medium">{holding.company_name}</p>
                    <p className="text-gray-300 text-sm">{holding.shares} shares @ {formatCurrency(holding.average_cost)}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-semibold">{formatCurrency(holding.market_value)}</p>
                  <p className={`text-sm ${
                    holding.unrealized_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {formatCurrency(holding.unrealized_gain_loss)} ({formatPercent(holding.unrealized_gain_loss_percent)})
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};
