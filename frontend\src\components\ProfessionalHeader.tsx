import React, { useState, useEffect, useRef } from 'react';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { TrendingUp, ChevronDown } from 'lucide-react';

interface ProfessionalHeaderProps {
  userTokens: number;
  userName: string;
  userTier?: string;
  onTokenPurchase: () => void;
  onResetToBasic?: () => void;
  onStartTrialPro?: () => void;
  onUpgradeToPro?: () => void;
}

export const ProfessionalHeader: React.FC<ProfessionalHeaderProps> = ({
  userTokens,
  userName,
  userTier = 'Basic',
  onResetToBasic,
  onStartTrialPro,
  onUpgradeToPro
}) => {
  const [showPlanDropdown, setShowPlanDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowPlanDropdown(false);
      }
    };

    if (showPlanDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPlanDropdown]);

  const toggleDropdown = () => {
    setShowPlanDropdown(!showPlanDropdown);
  };

  const handlePlanSelect = (planType: string) => {
    setShowPlanDropdown(false);

    switch (planType) {
      case 'basic':
        onResetToBasic?.();
        break;
      case 'trial':
        onStartTrialPro?.();
        break;
      case 'pro':
        onUpgradeToPro?.();
        break;
    }
  };

  return (
    <header className={`hot-card m-2 sm:m-4 mb-0 float-animation border-0 shadow-2xl relative z-[1000] ${
      userTier === 'Pro'
        ? 'bg-gradient-to-r from-purple-900/20 to-blue-900/20 border-2 border-purple-500/30'
        : 'bg-gradient-to-r from-gray-900/20 to-slate-900/20'
    }`}>
      <div className="container mx-auto px-3 sm:px-6 py-4 sm:py-6">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 lg:gap-0">
          {/* Brand Section */}
          <div className="flex items-center space-x-2 sm:space-x-4 w-full lg:w-auto">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="w-10 h-10 sm:w-12 sm:h-12 hot-card pulse-glow-blue flex items-center justify-center">
                <TrendingUp className="h-5 w-5 sm:h-7 sm:w-7 neon-text-blue" />
              </div>
              <div className="min-w-0 flex-1">
                <h1 className={`text-xl sm:text-2xl lg:text-3xl font-bold truncate ${
                  userTier === 'Pro'
                    ? 'bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent'
                    : 'gradient-text-hot'
                }`}>
                  FinanceGPT {userTier}
                  {userTier === 'Pro' && <span className="text-purple-400 ml-1 sm:ml-2">✨</span>}
                </h1>
                <p className="text-xs sm:text-sm text-gray-300 mt-1 hidden sm:block">
                  {userTier === 'Pro' ? 'Professional Stock Analysis Platform' : 'Smart Stock Analysis Platform'}
                </p>
              </div>
            </div>
          </div>

          {/* User Profile & Token Section */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 lg:gap-6 w-full lg:w-auto">
            {/* Token Counter */}
            <div className="text-center sm:text-left lg:text-center order-2 sm:order-1">
              <div className="text-xs sm:text-sm text-gray-300">Available Tokens</div>
              <div className={`text-lg sm:text-xl lg:text-2xl font-bold ${
                userTier === 'Pro' ? 'text-purple-400' : 'text-blue-400'
              }`}>
                {userTokens.toLocaleString()}
              </div>
              {userTier === 'Pro' && (
                <div className="text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-1 rounded-full font-medium inline-block mt-1">
                  PRO ACTIVE ✨
                </div>
              )}
            </div>

            {/* Plan Selection Dropdown */}
            <div className="relative order-1 sm:order-2" ref={dropdownRef}>
              <Button
                ref={buttonRef}
                onClick={toggleDropdown}
                className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-3 sm:px-4 py-2 flex items-center gap-2 text-sm"
              >
                <span className="hidden sm:inline">🚀 Switch Plans</span>
                <span className="sm:hidden">🚀 Plans</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${showPlanDropdown ? 'rotate-180' : ''}`} />
              </Button>

              {/* Dropdown Menu */}
              {showPlanDropdown && (
                <>
                  {/* Backdrop to prevent interaction with content below */}
                  <div className="fixed inset-0 z-[50000]" onClick={() => setShowPlanDropdown(false)} />

                  {/* Dropdown */}
                  <div className="absolute top-full right-0 mt-2 w-72 sm:w-80 bg-gray-900 border border-white/20 rounded-lg shadow-2xl z-[50001] backdrop-blur-sm max-h-[80vh] overflow-y-auto">
                  {/* Basic Plan */}
                  <div
                    onClick={() => handlePlanSelect('basic')}
                    className="p-3 sm:p-4 hover:bg-white/10 cursor-pointer border-b border-white/10 transition-colors"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-semibold flex items-center gap-2 flex-wrap">
                          <span className="truncate">FinanceGPT Basic</span>
                          {userTier === 'Basic' && (
                            <Badge className="bg-green-500 text-white text-xs shrink-0">CURRENT</Badge>
                          )}
                        </div>
                        <div className="text-gray-300 text-sm">500 tokens • 25 daily queries</div>
                      </div>
                      <div className="text-green-400 text-sm font-medium shrink-0">
                        {userTier === 'Basic' ? 'ACTIVE' : 'FREE'}
                      </div>
                    </div>
                  </div>

                  {/* Trial Pro Plan */}
                  <div
                    onClick={() => handlePlanSelect('trial')}
                    className="p-3 sm:p-4 hover:bg-white/10 cursor-pointer border-b border-white/10 transition-colors"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-semibold flex items-center gap-2 flex-wrap">
                          <span className="truncate">FinanceGPT Pro Trial</span>
                          <Badge className="bg-orange-500 text-white text-xs shrink-0">7 DAYS</Badge>
                        </div>
                        <div className="text-gray-300 text-sm">10,000 tokens • Unlimited queries</div>
                      </div>
                      <div className="text-orange-400 text-sm font-medium shrink-0">FREE</div>
                    </div>
                  </div>

                  {/* Pro Plan */}
                  <div
                    onClick={() => handlePlanSelect('pro')}
                    className="p-3 sm:p-4 hover:bg-white/10 cursor-pointer transition-colors"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                      <div className="min-w-0 flex-1">
                        <div className="text-white font-semibold flex items-center gap-2 flex-wrap">
                          <span className="truncate">FinanceGPT Pro</span>
                          {userTier === 'Pro' ? (
                            <Badge className="bg-green-500 text-white text-xs shrink-0">CURRENT</Badge>
                          ) : (
                            <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-xs shrink-0">POPULAR</Badge>
                          )}
                        </div>
                        <div className="text-gray-300 text-sm">10,000 tokens • All features</div>
                      </div>
                      <div className="text-blue-400 text-sm font-medium shrink-0">
                        {userTier === 'Pro' ? 'ACTIVE' : '$29/mo'}
                      </div>
                    </div>
                  </div>
                </div>
                </>
              )}
            </div>

            {/* User Profile */}
            <div className="flex items-center space-x-2 sm:space-x-3 order-3 sm:order-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shrink-0">
                <span className="text-white font-bold text-sm sm:text-base">
                  {userName.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="min-w-0 hidden sm:block">
                <div className="text-white font-medium truncate">{userName}</div>
                <div className="text-sm text-gray-300 truncate">FinanceGPT {userTier}</div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </header>
  );
};
