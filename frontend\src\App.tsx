import { useState, useEffect, useRef } from "react";
import "./global.css";
import { ProfessionalHeader } from "./components/ProfessionalHeader";
import { InteractiveOptionsMenu } from "./components/InteractiveOptionsMenu";

import { NavigationTabs } from "./components/NavigationTabs";
import { PricingPage } from "./components/PricingPage";
import { PlanSelectionModal } from "./components/PlanSelectionModal";
import { AuthModal } from "./components/AuthModal";

import { AdvancedAnalytics } from "./components/AdvancedAnalytics";
import { PersonalizedChat } from "./components/PersonalizedChat";
import { PortfolioTracker } from "./components/PortfolioTrackerSimple";
import { Card } from "./components/ui/card";
import { Input } from "./components/ui/input";
import { Button } from "./components/ui/button";

import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { SUBSCRIPTION_TIERS } from "./types/subscription";

interface AnalysisOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  tokenCost: number;
  category: 'basic' | 'advanced' | 'premium';
}

// Removed ChatMessage interface as we now use PersonalizedChat component

function AppContent() {
  const { user, loading, checkUsageLimit, incrementUsage, resetDailyUsage, resetDemoUser, upgradeToProDemo, startProTrial } = useAuth();



  // Show loading screen while auth is initializing
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center"
           style={{
             background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
             color: '#ffffff'
           }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white">Loading FinanceGPT...</h2>
        </div>
      </div>
    );
  }

  // Show error if no user (shouldn't happen with demo user)
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center"
           style={{
             background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
             color: '#ffffff'
           }}>
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-400 mb-4">Error: No User Found</h2>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }
  const [input, setInput] = useState("");
  const [response, setResponse] = useState("");
  const [chartData, setChartData] = useState<any>(null);
  const [detectedTicker, setDetectedTicker] = useState<string | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to results when they appear
  useEffect(() => {
    if (response && resultsRef.current) {
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 100);
    }
  }, [response]);


  const [activeTab, setActiveTab] = useState("dashboard");
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showPlanSelectionModal, setShowPlanSelectionModal] = useState(false);

  // Removed chat-specific state as we now use PersonalizedChat component

  // Detect ticker from input
  useEffect(() => {
    const tickerRegex = /\b[A-Z]{1,5}\b/;
    const match = input.match(tickerRegex);
    setDetectedTicker(match ? match[0] : null);
  }, [input]);

  const handleOptionSelect = async (option: AnalysisOption, ticker: string) => {
    // Check if feature is available for user's tier
    const tier = SUBSCRIPTION_TIERS[user?.subscription.tier || 'free'];

    // Only restrict premium features for free users
    if (option.category === 'premium' && !tier.features.comprehensiveReports) {
      setShowPricingModal(true);
      return;
    }

    // Check usage limits
    if (!checkUsageLimit('analysis')) {
      setShowPricingModal(true);
      return;
    }

    setIsAnalyzing(true);

    try {
      // Construct query based on option
      let query = "";
      switch (option.id) {
        case 'price-check':
          query = `What's ${ticker}'s current price?`;
          break;
        case 'technical-analysis':
          query = `Provide technical analysis for ${ticker}`;
          break;
        case 'sentiment-analysis':
          query = `What's the sentiment for ${ticker}?`;
          break;
        case 'fundamental-analysis':
          query = `Analyze ${ticker}'s fundamentals`;
          break;
        case 'investment-recommendation':
          query = `Should I buy ${ticker} stock?`;
          break;
        case 'comprehensive-analysis':
          query = `Provide comprehensive analysis for ${ticker}`;
          break;
        default:
          query = `Analyze ${ticker}`;
      }

      const res = await fetch(`http://localhost:8125/analyze`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query }),
      });

      const data = await res.json();

      if (data.messages && data.messages.length > 0) {
        const lastMessage = data.messages[data.messages.length - 1];
        setResponse(lastMessage.content + "\n\n⚠️ Disclaimer: This is not financial advice. Please consult with a qualified financial advisor before making investment decisions.");
      } else {
        setResponse("Analysis completed, but no response received.");
      }

      setChartData(data.chartData || null);
      incrementUsage(option.tokenCost);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unknown error occurred.";
      setResponse(`Error: ${errorMessage}\n\n⚠️ Disclaimer: This is not financial advice. Please consult with a qualified financial advisor before making investment decisions.`);
      setChartData(null);

      // Note: In a real app, you might want to refund usage on error
      // For now, we'll keep the usage as is
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleManualSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const tickerRegex = /\b[A-Z]{1,5}\b/;
    if (!tickerRegex.test(input)) {
      setResponse("Please enter a valid stock ticker (1-5 uppercase letters, e.g., AAPL) in your query.");
      return;
    }

    // Check usage limits
    if (!checkUsageLimit('analysis')) {
      setShowPricingModal(true);
      return;
    }

    setIsAnalyzing(true);

    try {
      const res = await fetch(`http://localhost:8125/analyze`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query: input }),
      });

      const data = await res.json();

      if (data.messages && data.messages.length > 0) {
        const lastMessage = data.messages[data.messages.length - 1];
        setResponse(lastMessage.content + "\n\n⚠️ Disclaimer: This is not financial advice. Please consult with a qualified financial advisor before making investment decisions.");
      } else {
        setResponse("Analysis completed, but no response received.");
      }

      setChartData(data.chartData || null);
      incrementUsage(25); // Manual query cost
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unknown error occurred.";
      setResponse(`Error: ${errorMessage}\n\n⚠️ Disclaimer: This is not financial advice. Please consult with a qualified financial advisor before making investment decisions.`);
      setChartData(null);

      // Note: In a real app, you might want to refund usage on error
      // For now, we'll keep the usage as is
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Reset daily usage on component mount
  useEffect(() => {
    resetDailyUsage();
  }, [resetDailyUsage]);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'pricing':
        return (
          <PricingPage
            onUpgrade={() => setShowPricingModal(false)}
            onUpgradeToProDemo={() => {
              upgradeToProDemo();
              setShowPricingModal(false);
            }}
          />
        );
      case 'chat':
        return (
          <div className="max-w-6xl mx-auto h-[calc(100vh-200px)]">
            <PersonalizedChat
              userTokens={(user?.usage.monthlyLimit || 0) - (user?.usage.monthlyTokens || 0)}
              userTier={user?.subscription.tier === 'pro' ? 'Pro' : 'Basic'}
              onTokenDeduct={(amount) => incrementUsage(amount)}
            />
          </div>
        );
      case 'analytics':
        return (
          <AdvancedAnalytics
            userTokens={(user?.usage.monthlyLimit || 0) - (user?.usage.monthlyTokens || 0)}
            onTokenDeduct={(amount) => incrementUsage(amount)}
          />
        );



      case 'portfolio':
        return (
          <PortfolioTracker
            userTokens={(user?.usage.monthlyLimit || 0) - (user?.usage.monthlyTokens || 0)}
            userTier={user?.subscription.tier === 'pro' ? 'Pro' : 'Basic'}
            onTokenDeduct={(amount: number) => incrementUsage(amount)}
          />
        );

      case 'dashboard':
      default:
        return (
          <div className="space-y-6 sm:space-y-8">
            {/* Plan Status Banner */}
            <div className={`p-4 sm:p-6 rounded-lg border ${
              user?.subscription.tier === 'pro'
                ? 'bg-gradient-to-r from-purple-900/10 to-blue-900/10 border-purple-500/30'
                : 'bg-gradient-to-r from-gray-800/10 to-slate-800/10 border-gray-500/30'
            }`}>
              <div className="text-center">
                <h2 className={`text-2xl sm:text-3xl font-bold mb-3 ${
                  user?.subscription.tier === 'pro'
                    ? 'bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent'
                    : 'text-white'
                }`}>
                  {user?.subscription.tier === 'pro' ? '🚀 FinanceGPT Pro Dashboard' : '📊 FinanceGPT Basic Dashboard'}
                </h2>
                <p className="text-gray-300 text-base sm:text-lg">
                  {user?.subscription.tier === 'pro'
                    ? 'Welcome to your premium financial analysis platform!'
                    : 'Welcome to FinanceGPT Basic. Upgrade to Pro for advanced features.'}
                </p>
                {user?.subscription.tier === 'pro' && (
                  <div className="mt-4">
                    <span className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-xs sm:text-sm font-medium">
                      ✨ All Premium Features Unlocked
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Pro Features Showcase - Only for Pro Users */}
            {user?.subscription.tier === 'pro' && (
              <div className="grid grid-cols-1 gap-3 sm:gap-4">
                <Card className="p-3 sm:p-4 bg-gradient-to-br from-purple-900/20 to-blue-900/20 border-purple-500/30">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-sm sm:text-base shrink-0">
                      🎯
                    </div>
                    <div className="min-w-0">
                      <h3 className="text-white font-semibold text-sm sm:text-base">Portfolio Tracking</h3>
                      <p className="text-gray-300 text-xs sm:text-sm">Portfolio management</p>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {/* Manual Input Section */}
            <Card className="p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-semibold text-white mb-4">
                Custom Analysis Query
              </h3>
              <form onSubmit={handleManualSubmit} className="flex flex-col sm:flex-row gap-3 sm:gap-4">
                <Input
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value.toUpperCase())}
                  placeholder="Enter your stock analysis query (e.g., 'Analyze AAPL stock')"
                  className="flex-1 text-sm sm:text-base"
                  disabled={isAnalyzing}
                />
                <Button
                  type="submit"
                  disabled={isAnalyzing || !input.trim()}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 text-sm sm:text-base whitespace-nowrap"
                >
                  {isAnalyzing ? "Analyzing..." : "Analyze (25 tokens)"}
                </Button>
              </form>
            </Card>

            {/* Interactive Options Menu */}
            <InteractiveOptionsMenu
              detectedTicker={detectedTicker}
              userTokens={(user?.usage.monthlyLimit || 0) - (user?.usage.monthlyTokens || 0)}
              onOptionSelect={handleOptionSelect}
            />

            {/* Results Section */}
            {response && (
              <Card className="p-4 sm:p-6" ref={resultsRef}>
                <h3 className="text-base sm:text-lg font-semibold text-white mb-4">
                  Analysis Results
                </h3>
                <div className="prose max-w-none">
                  <pre className="whitespace-pre-wrap text-xs sm:text-sm text-gray-100 bg-white/10 backdrop-blur-sm p-3 sm:p-4 rounded-lg border border-white/20 overflow-x-auto">
                    {response}
                  </pre>
                </div>
              </Card>
            )}

            {/* Chart Section */}
            {chartData && (
              <Card className="p-4 sm:p-6">
                <h3 className="text-base sm:text-lg font-semibold text-white mb-4">
                  Chart Data
                </h3>
                <div className="chart-container overflow-x-auto">
                  <canvas id="stockChart" style={{ width: "100%", height: "250px", minHeight: "200px" }}></canvas>
                </div>
              </Card>
            )}
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen"
         style={{
           background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
           color: '#ffffff'
         }}>

      {/* Professional Header */}
      <ProfessionalHeader
        userTokens={(user?.usage.monthlyLimit || 0) - (user?.usage.monthlyTokens || 0)}
        userName={user?.name || 'Demo User'}
        userTier={user?.subscription.tier === 'pro' ? 'Pro' : 'Basic'}
        onTokenPurchase={() => setShowPricingModal(true)}
        onResetToBasic={resetDemoUser}
        onStartTrialPro={startProTrial}
        onUpgradeToPro={upgradeToProDemo}
      />

      {/* Navigation Tabs */}
      <NavigationTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        userTokens={(user?.usage.monthlyLimit || 0) - (user?.usage.monthlyTokens || 0)}
        userTier={user?.subscription.tier === 'pro' ? 'Pro' : 'Basic'}
      />



      {/* Main Content */}
      <main className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8">
        {renderTabContent()}
      </main>

      {/* Plan Selection Modal */}
      <PlanSelectionModal
        isOpen={showPlanSelectionModal}
        onClose={() => setShowPlanSelectionModal(false)}
        currentTier={user?.subscription.tier === 'pro' ? 'Pro' : 'Basic'}
        onSelectBasic={() => {
          resetDemoUser();
        }}
        onSelectTrialPro={() => {
          startProTrial();
        }}
        onSelectPro={() => {
          upgradeToProDemo();
        }}
      />

      {/* Pricing Modal */}
      {showPricingModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg sm:rounded-xl border border-white/20 max-w-4xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
            <div className="p-4 sm:p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl sm:text-2xl font-bold text-white">Upgrade Your Plan</h2>
                <button
                  onClick={() => setShowPricingModal(false)}
                  className="text-gray-400 hover:text-white p-2 touch-manipulation"
                >
                  ✕
                </button>
              </div>
              <PricingPage
                onUpgrade={() => setShowPricingModal(false)}
                onUpgradeToProDemo={() => {
                  upgradeToProDemo();
                  setShowPricingModal(false);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </div>
  );
}

// Main App component with AuthProvider
function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;