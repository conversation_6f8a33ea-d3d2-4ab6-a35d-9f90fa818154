import React from 'react';
import {
  Zap,
  Home,
  PieChart,
  MessageCircle
} from 'lucide-react';

interface TabConfig {
  id: string;
  name: string;
  icon: any;
  description: string;
  color: string;
  bgColor: string;
  borderColor: string;
  comingSoon?: boolean;
  proOnly?: boolean;
}

interface NavigationTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  userTokens: number;
  userTier?: 'Basic' | 'Pro';
}

export const NavigationTabs: React.FC<NavigationTabsProps> = ({
  activeTab,
  onTabChange,
  userTier = 'Basic'
}) => {
  const tabs: TabConfig[] = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: Home,
      description: 'Main analysis dashboard',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'chat',
      name: 'AI Chat',
      icon: MessageCircle,
      description: 'Chat with your AI financial advisor',
      color: 'text-cyan-600',
      bgColor: 'bg-cyan-50',
      borderColor: 'border-cyan-200'
    },
    {
      id: 'analytics',
      name: 'Advanced Analytics',
      icon: Zap,
      description: 'Professional analysis tools',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },

    {
      id: 'portfolio',
      name: 'Portfolio Tracker',
      icon: PieChart,
      description: 'Track your investments',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      proOnly: true
    },

  ];

  return (
    <div className={`glass-card mx-2 sm:mx-4 mt-2 sm:mt-4 border-0 shadow-xl sticky top-2 sm:top-4 z-[100] ${
      userTier === 'Pro'
        ? 'bg-gradient-to-r from-purple-900/10 to-blue-900/10 border border-purple-500/20'
        : ''
    }`}>
      <div className="container mx-auto px-3 sm:px-6">
        {/* Navigation Tabs with Horizontal Scroll */}
        <nav className="flex space-x-1 sm:space-x-2 overflow-x-auto py-4 sm:py-6 scrollbar-hide">
          <div className="flex space-x-1 sm:space-x-2 min-w-max">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              const isActive = activeTab === tab.id;
              const isProOnly = tab.proOnly && userTier === 'Basic';
              const isDisabled = tab.comingSoon || isProOnly;

              return (
                <button
                  key={tab.id}
                  onClick={() => !isDisabled && onTabChange(tab.id)}
                  disabled={isDisabled}
                  className={`
                    hot-nav-tab flex items-center space-x-2 sm:space-x-3 px-3 sm:px-4 lg:px-6 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-semibold text-xs sm:text-sm transition-all duration-300 whitespace-nowrap min-h-[44px] touch-manipulation
                    ${isActive
                      ? `hot-card text-white shadow-lg`
                      : 'text-white/80 hover:text-white hover:bg-white/10'
                    }
                    ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:scale-105 active:scale-95'}
                  `}
                >
                  <IconComponent className={`h-4 w-4 sm:h-5 sm:w-5 shrink-0 ${isActive ? 'text-blue-400' : 'text-white/70'}`} />
                  <span className={`${isActive ? 'text-white' : ''} hidden sm:inline`}>{tab.name}</span>
                  <span className={`${isActive ? 'text-white' : ''} sm:hidden text-xs`}>
                    {tab.name.split(' ')[0]}
                  </span>
                  {tab.comingSoon && (
                    <span className="text-xs bg-orange-600 text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full shrink-0">
                      <span className="hidden sm:inline">Soon</span>
                      <span className="sm:hidden">S</span>
                    </span>
                  )}
                  {isProOnly && (
                    <span className="text-xs bg-gradient-to-r from-blue-500 to-purple-500 text-white px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full shrink-0">
                      <span className="hidden sm:inline">Pro</span>
                      <span className="sm:hidden">P</span>
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </nav>

        {/* Tab Description */}
        <div className="pb-4 sm:pb-6">
          {tabs.map((tab) => {
            if (activeTab !== tab.id) return null;

            return (
              <div key={tab.id}>
                <p className="text-xs sm:text-sm text-gray-300 font-semibold px-1">{tab.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
